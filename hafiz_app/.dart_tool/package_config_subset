_fe_analyzer_shared
3.3
file:///home/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/lib/
analyzer
3.3
file:///home/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.11.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.11.0/lib/
args
3.3
file:///home/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/lib/
async
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/
auto_route
3.0
file:///home/<USER>/.pub-cache/hosted/pub.dev/auto_route-8.3.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/auto_route-8.3.0/lib/
auto_route_generator
3.3
file:///home/<USER>/.pub-cache/hosted/pub.dev/auto_route_generator-8.1.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/auto_route_generator-8.1.0/lib/
boolean_selector
3.1
file:///home/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/
file:///home/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/lib/
build
3.6
file:///home/<USER>/.pub-cache/hosted/pub.dev/build-2.4.2/
file:///home/<USER>/.pub-cache/hosted/pub.dev/build-2.4.2/lib/
build_config
3.6
file:///home/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/
file:///home/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/lib/
build_daemon
3.6
file:///home/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/
file:///home/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/lib/
build_resolvers
3.6
file:///home/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.4/
file:///home/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.4/lib/
build_runner
3.6
file:///home/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.14/
file:///home/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.14/lib/
build_runner_core
3.6
file:///home/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-8.0.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-8.0.0/lib/
built_collection
2.12
file:///home/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/lib/
built_value
3.0
file:///home/<USER>/.pub-cache/hosted/pub.dev/built_value-8.11.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/built_value-8.11.0/lib/
characters
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/
checked_yaml
3.8
file:///home/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4/
file:///home/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4/lib/
clock
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/
file:///home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/
code_builder
3.5
file:///home/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/lib/
collection
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/
convert
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/
file:///home/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/lib/
crypto
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/
file:///home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/
cupertino_icons
3.1
file:///home/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///home/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
dart_style
3.0
file:///home/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.8/
file:///home/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.8/lib/
dartz
2.12
file:///home/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/
fake_async
3.3
file:///home/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/
file:///home/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/lib/
file
3.0
file:///home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/
fixnum
3.1
file:///home/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/
flutter_lints
3.5
file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/lib/
flutter_riverpod
2.17
file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/
freezed
3.0
file:///home/<USER>/.pub-cache/hosted/pub.dev/freezed-2.5.7/
file:///home/<USER>/.pub-cache/hosted/pub.dev/freezed-2.5.7/lib/
freezed_annotation
3.0
file:///home/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4/
file:///home/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4/lib/
frontend_server_client
3.0
file:///home/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/lib/
get_it
3.0
file:///home/<USER>/.pub-cache/hosted/pub.dev/get_it-8.1.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/get_it-8.1.0/lib/
glob
3.3
file:///home/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/
file:///home/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/lib/
graphs
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/
file:///home/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/lib/
http_multi_server
3.2
file:///home/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/
file:///home/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/lib/
http_parser
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/
file:///home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/
io
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/
file:///home/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/lib/
js
3.7
file:///home/<USER>/.pub-cache/hosted/pub.dev/js-0.7.2/
file:///home/<USER>/.pub-cache/hosted/pub.dev/js-0.7.2/lib/
json_annotation
3.0
file:///home/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/
json_serializable
3.5
file:///home/<USER>/.pub-cache/hosted/pub.dev/json_serializable-6.9.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/json_serializable-6.9.0/lib/
leak_tracker
3.2
file:///home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/
file:///home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/lib/
leak_tracker_flutter_testing
3.2
file:///home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.6
file:///home/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/lib/
logging
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/
macros
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/macros-0.1.3-main.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/macros-0.1.3-main.0/lib/
matcher
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/
file:///home/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
2.17
file:///home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
meta
2.12
file:///home/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/
mime
3.2
file:///home/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/
package_config
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/lib/
path
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/
pedantic
2.12
file:///home/<USER>/.pub-cache/hosted/pub.dev/pedantic-1.11.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/pedantic-1.11.1/lib/
permission_handler
3.5
file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/lib/
permission_handler_android
3.5
file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/lib/
permission_handler_apple
2.18
file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/
file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/lib/
permission_handler_html
3.3
file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib/
permission_handler_platform_interface
3.5
file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/
permission_handler_windows
2.12
file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib/
petitparser
3.5
file:///home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/
platform
3.2
file:///home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/
file:///home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///home/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///home/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
pool
2.12
file:///home/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/lib/
pub_semver
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/lib/
pubspec_parse
3.6
file:///home/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/lib/
riverpod
2.17
file:///home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/
shelf
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/
file:///home/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/lib/
shelf_web_socket
3.3
file:///home/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-2.0.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-2.0.1/lib/
source_gen
3.0
file:///home/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0/lib/
source_helper
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.5/
file:///home/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.5/lib/
source_span
3.1
file:///home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/
speech_to_text
3.0
file:///home/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.2/
file:///home/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.2/lib/
speech_to_text_macos
3.0
file:///home/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_macos-1.1.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_macos-1.1.0/lib/
speech_to_text_platform_interface
3.0
file:///home/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_platform_interface-2.3.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_platform_interface-2.3.0/lib/
sprintf
2.12
file:///home/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/
sqflite
3.7
file:///home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/
file:///home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/
sqflite_android
3.7
file:///home/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/lib/
sqflite_common
3.8
file:///home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/
file:///home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/
sqflite_darwin
3.7
file:///home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/
file:///home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/lib/
sqflite_platform_interface
3.5
file:///home/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/
stack_trace
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/
state_notifier
2.12
file:///home/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/lib/
stream_channel
3.3
file:///home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/
file:///home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/
stream_transform
3.1
file:///home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/
string_scanner
3.1
file:///home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/
synchronized
3.8
file:///home/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/
term_glyph
3.1
file:///home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/
file:///home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/
test_api
3.5
file:///home/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/
file:///home/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/lib/
timing
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/
file:///home/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/lib/
typed_data
3.5
file:///home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/
uuid
3.0
file:///home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/
vector_math
2.14
file:///home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/
file:///home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/
vm_service
3.3
file:///home/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/lib/
watcher
3.1
file:///home/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/
file:///home/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/lib/
web
3.3
file:///home/<USER>/.pub-cache/hosted/pub.dev/web-0.5.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/web-0.5.1/lib/
web_socket
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/
file:///home/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib/
web_socket_channel
3.3
file:///home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/
file:///home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/
xml
3.2
file:///home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/
file:///home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/
yaml
3.4
file:///home/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/
file:///home/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/lib/
_macros
3.5
file:///home/<USER>/Applications/flutter/bin/cache/dart-sdk/pkg/_macros/
file:///home/<USER>/Applications/flutter/bin/cache/dart-sdk/pkg/_macros/lib/
sky_engine
3.7
file:///home/<USER>/Applications/flutter/bin/cache/pkg/sky_engine/
file:///home/<USER>/Applications/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///home/<USER>/Applications/flutter/packages/flutter/
file:///home/<USER>/Applications/flutter/packages/flutter/lib/
flutter_test
3.7
file:///home/<USER>/Applications/flutter/packages/flutter_test/
file:///home/<USER>/Applications/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.7
file:///home/<USER>/Applications/flutter/packages/flutter_web_plugins/
file:///home/<USER>/Applications/flutter/packages/flutter_web_plugins/lib/
hafiz_app
3.8
file:///home/<USER>/coding/flutter-app/hafiz_app/
file:///home/<USER>/coding/flutter-app/hafiz_app/lib/
2
