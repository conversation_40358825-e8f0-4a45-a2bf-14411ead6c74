 /home/<USER>/coding/flutter-app/hafiz_app/build/flutter_assets/assets/audio/error.mp3 /home/<USER>/coding/flutter-app/hafiz_app/build/flutter_assets/assets/audio/success.mp3 /home/<USER>/coding/flutter-app/hafiz_app/build/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf /home/<USER>/coding/flutter-app/hafiz_app/build/flutter_assets/fonts/MaterialIcons-Regular.otf /home/<USER>/coding/flutter-app/hafiz_app/build/flutter_assets/shaders/ink_sparkle.frag /home/<USER>/coding/flutter-app/hafiz_app/build/flutter_assets/AssetManifest.json /home/<USER>/coding/flutter-app/hafiz_app/build/flutter_assets/AssetManifest.bin /home/<USER>/coding/flutter-app/hafiz_app/build/flutter_assets/FontManifest.json /home/<USER>/coding/flutter-app/hafiz_app/build/flutter_assets/NOTICES.Z /home/<USER>/coding/flutter-app/hafiz_app/build/flutter_assets/version.json /home/<USER>/coding/flutter-app/hafiz_app/build/flutter_assets/NativeAssetsManifest.json:  /home/<USER>/coding/flutter-app/hafiz_app/pubspec.yaml /home/<USER>/coding/flutter-app/hafiz_app/assets/audio/error.mp3 /home/<USER>/coding/flutter-app/hafiz_app/assets/audio/success.mp3 /home/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf /home/<USER>/Applications/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf /home/<USER>/Applications/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag /home/<USER>/coding/flutter-app/hafiz_app/.dart_tool/flutter_build/a89c78a20b46501f41f646933c5bf209/native_assets.json /home/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.11.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/auto_route-8.3.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/auto_route_generator-8.1.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/build-2.4.2/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.4/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.14/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-8.0.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/built_value-8.11.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.8/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/freezed-2.5.7/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/get_it-8.1.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/js-0.7.2/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/json_serializable-6.9.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/macros-0.1.3-main.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/pedantic-1.11.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-2.0.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.5/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.2/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_macos-1.1.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_platform_interface-2.3.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/web-0.5.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE /home/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE /home/<USER>/Applications/flutter/bin/cache/dart-sdk/pkg/_macros/LICENSE /home/<USER>/Applications/flutter/bin/cache/pkg/sky_engine/LICENSE /home/<USER>/Applications/flutter/packages/flutter/LICENSE /home/<USER>/coding/flutter-app/hafiz_app/DOES_NOT_EXIST_RERUN_FOR_WILDCARD668508228