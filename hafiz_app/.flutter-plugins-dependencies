{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "permission_handler_apple", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "speech_to_text", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "permission_handler_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "speech_to_text", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "speech_to_text_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_macos-1.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [], "windows": [{"name": "permission_handler_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "permission_handler_html", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": [], "dev_dependency": false}, {"name": "speech_to_text", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.2/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "speech_to_text", "dependencies": ["speech_to_text_macos"]}, {"name": "speech_to_text_macos", "dependencies": []}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}], "date_created": "2025-08-02 13:58:38.541568", "version": "3.32.8", "swift_package_manager_enabled": {"ios": false, "macos": false}}