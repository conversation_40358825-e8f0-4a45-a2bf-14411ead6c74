import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'router/app_router.dart';
import 'theme/app_theme.dart';

class Ha<PERSON>zApp extends ConsumerWidget {
  final AppRouter _appRouter = AppRouter();
  
  HafizApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp.router(
      title: 'Hafiz - Arabic Memorization Assistant',
      theme: AppTheme.lightTheme,
      routerConfig: _appRouter.config(),
      debugShowCheckedModeBanner: false,
      
      // Localization support
      locale: const Locale('en', 'US'),
      supportedLocales: const [
        Locale('en', 'US'), // English (default)
        Locale('ar', 'SA'), // Arabic (future support)
      ],
    );
  }
}
