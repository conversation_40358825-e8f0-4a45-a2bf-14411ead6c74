import 'package:audioplayers/audioplayers.dart';

class AudioUtils {
  static final AudioPlayer _audioPlayer = AudioPlayer();
  
  /// Plays success sound effect
  static Future<void> playSuccessSound() async {
    try {
      await _audioPlayer.play(AssetSource('audio/success.mp3'));
    } catch (e) {
      // Silently fail if audio can't be played
      print('Failed to play success sound: $e');
    }
  }
  
  /// Plays error sound effect
  static Future<void> playErrorSound() async {
    try {
      await _audioPlayer.play(AssetSource('audio/error.mp3'));
    } catch (e) {
      // Silently fail if audio can't be played
      print('Failed to play error sound: $e');
    }
  }
  
  /// Stops any currently playing audio
  static Future<void> stopAudio() async {
    try {
      await _audioPlayer.stop();
    } catch (e) {
      print('Failed to stop audio: $e');
    }
  }
  
  /// Sets the volume for audio playback (0.0 to 1.0)
  static Future<void> setVolume(double volume) async {
    try {
      await _audioPlayer.setVolume(volume.clamp(0.0, 1.0));
    } catch (e) {
      print('Failed to set volume: $e');
    }
  }
  
  /// Disposes the audio player
  static Future<void> dispose() async {
    try {
      await _audioPlayer.dispose();
    } catch (e) {
      print('Failed to dispose audio player: $e');
    }
  }
}
