extension StringExtensions on String {
  /// Splits Arabic text into words, handling Arabic punctuation and spaces
  List<String> get arabicWords {
    // Remove extra whitespace and split by spaces
    return trim()
        .replaceAll(RegExp(r'\s+'), ' ')
        .split(' ')
        .where((word) => word.isNotEmpty)
        .toList();
  }
  
  /// Checks if the string contains Arabic characters
  bool get hasArabicCharacters {
    return RegExp(r'[\u0600-\u06FF]').hasMatch(this);
  }
  
  /// Normalizes Arabic text by removing extra spaces and normalizing characters
  String get normalizedArabic {
    return trim()
        .replaceAll(RegExp(r'\s+'), ' ')
        .replaceAll('ي', 'ى') // Normalize Yaa
        .replaceAll('ة', 'ه'); // Normalize Taa Marbouta (optional)
  }
  
  /// Removes Arabic diacritics (tashkeel) from the text
  String get withoutDiacritics {
    return replaceAll(RegExp(r'[\u064B-\u0652\u0670\u0640]'), '');
  }
  
  /// Checks if the string is a valid Arabic text (contains Arabic characters)
  bool get isValidArabicText {
    return isNotEmpty && hasArabicCharacters;
  }
  
  /// Capitalizes the first letter of the string
  String get capitalized {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1)}';
  }
  
  /// Truncates the string to a maximum length with ellipsis
  String truncate(int maxLength, {String ellipsis = '...'}) {
    if (length <= maxLength) return this;
    return '${substring(0, maxLength - ellipsis.length)}$ellipsis';
  }
  
  /// Checks if two Arabic words are exactly the same (including diacritics)
  bool isExactArabicMatch(String other) {
    return trim() == other.trim();
  }
  
  /// Checks if two Arabic words are the same without diacritics
  bool isArabicMatchWithoutDiacritics(String other) {
    return withoutDiacritics.trim() == other.withoutDiacritics.trim();
  }
}
